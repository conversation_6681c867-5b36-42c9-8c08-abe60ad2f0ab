{"logs": [{"outputFile": "com.example.petcare.app-mergeDebugResources-45:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a800a360ad9d097b56329a7af7139d0\\transformed\\browser-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5853,6053,6158,6273", "endColumns": "112,104,114,100", "endOffsets": "5961,6153,6268,6369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a74bb36ab68b4d1ac52e597a10bbe4d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4558", "endColumns": "136", "endOffsets": "4690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6bb2d0e97b623151957e955274b89d8a\\transformed\\preference-1.2.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,344,488,657,743", "endColumns": "70,86,80,143,168,85,81", "endOffsets": "171,258,339,483,652,738,820"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5782,5966,6374,6455,6784,6953,7039", "endColumns": "70,86,80,143,168,85,81", "endOffsets": "5848,6048,6450,6594,6948,7034,7116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\895788cfce406ee7adbc1ad1d8f4cebb\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2820,2918,3020,3117,3215,3320,3423,6683", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "2913,3015,3112,3210,3315,3418,3534,6779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19338aeb57a6075f97d371116308faf5\\transformed\\appcompat-1.1.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,904,995,1087,1183,1277,1378,1471,1566,1662,1753,1844,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,81,90,91,95,93,100,92,94,95,90,90,85,105,105,100,106,111,103,155,97,83", "endOffsets": "208,312,420,506,614,733,817,899,990,1082,1178,1272,1373,1466,1561,1657,1748,1839,1925,2031,2137,2238,2345,2457,2561,2717,2815,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,904,995,1087,1183,1277,1378,1471,1566,1662,1753,1844,1930,2036,2142,2243,2350,2462,2566,2722,6599", "endColumns": "107,103,107,85,107,118,83,81,90,91,95,93,100,92,94,95,90,90,85,105,105,100,106,111,103,155,97,83", "endOffsets": "208,312,420,506,614,733,817,899,990,1082,1178,1272,1373,1466,1561,1657,1748,1839,1925,2031,2137,2238,2345,2457,2561,2717,2815,6678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fb89e9541d65ec02c19ff3a6e8c0440\\transformed\\jetified-play-services-base-18.5.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3539,3646,3807,3940,4050,4195,4328,4448,4695,4852,4959,5125,5258,5411,5570,5639,5703", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "3641,3802,3935,4045,4190,4323,4443,4553,4847,4954,5120,5253,5406,5565,5634,5698,5777"}}]}]}