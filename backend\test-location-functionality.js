const axios = require('axios');
const http = require('http');

const BASE_URL = 'http://localhost:5000/api';

// Test credentials
const businessUser = {
  email: '<EMAIL>',
  password: 'password123'
};

const petOwner = {
  email: '<EMAIL>', 
  password: 'password123'
};

let businessToken = '';
let petOwnerToken = '';

async function testLocationFunctionality() {
  console.log('🧪 Testing Location Functionality...\n');

  try {
    // Step 1: Login as business user
    console.log('1. Logging in as business user...');
    const businessLogin = await axios.post(`${BASE_URL}/auth/login`, businessUser);
    businessToken = businessLogin.data.token;
    console.log('✅ Business user logged in successfully\n');

    // Step 2: Login as pet owner
    console.log('2. Logging in as pet owner...');
    const petOwnerLogin = await axios.post(`${BASE_URL}/auth/login`, petOwner);
    petOwnerToken = petOwnerLogin.data.token;
    console.log('✅ Pet owner logged in successfully\n');

    // Step 3: Set business location
    console.log('3. Setting business location...');
    const locationData = {
      latitude: 37.7749,
      longitude: -122.4194,
      formattedAddress: '123 Business St, San Francisco, CA 94102'
    };

    const setLocationResponse = await axios.put(
      `${BASE_URL}/profile/update-location`,
      locationData,
      {
        headers: { Authorization: `Bearer ${businessToken}` }
      }
    );
    console.log('✅ Business location set successfully');
    console.log(`   Location: ${locationData.formattedAddress}`);
    console.log(`   Coordinates: ${locationData.latitude}, ${locationData.longitude}\n`);

    // Step 4: Get business location
    console.log('4. Retrieving business location...');
    const getLocationResponse = await axios.get(
      `${BASE_URL}/location/business-location`,
      {
        headers: { Authorization: `Bearer ${businessToken}` }
      }
    );
    console.log('✅ Business location retrieved successfully');
    console.log(`   Has Location: ${getLocationResponse.data.location.hasLocation}`);
    console.log(`   Address: ${getLocationResponse.data.location.formattedAddress}\n`);

    // Step 5: Search for nearby businesses (as pet owner)
    console.log('5. Searching for nearby businesses...');
    const nearbyBusinessesData = {
      latitude: 37.7849, // Slightly different location
      longitude: -122.4094,
      radius: 5 // 5km radius
    };

    const nearbyBusinessesResponse = await axios.post(
      `${BASE_URL}/location/nearby-businesses`,
      nearbyBusinessesData,
      {
        headers: { Authorization: `Bearer ${petOwnerToken}` }
      }
    );
    
    console.log('✅ Nearby businesses search completed');
    console.log(`   Found ${nearbyBusinessesResponse.data.businesses.length} businesses`);
    
    if (nearbyBusinessesResponse.data.businesses.length > 0) {
      const business = nearbyBusinessesResponse.data.businesses[0];
      console.log(`   First business: ${business.name}`);
      console.log(`   Distance: ${business.distance} km`);
      console.log(`   Address: ${business.formattedAddress || 'Not specified'}\n`);
    }

    // Step 6: Test location-based business search
    console.log('6. Testing location-based business search...');
    const searchResponse = await axios.get(
      `${BASE_URL}/location/search-businesses?latitude=37.7849&longitude=-122.4094&radius=10&query=business`,
      {
        headers: { Authorization: `Bearer ${petOwnerToken}` }
      }
    );
    
    console.log('✅ Location-based search completed');
    console.log(`   Found ${searchResponse.data.businesses.length} businesses matching criteria\n`);

    // Step 7: Update business location
    console.log('7. Updating business location...');
    const updatedLocationData = {
      latitude: 37.7849,
      longitude: -122.4094,
      formattedAddress: '456 Updated Business Ave, San Francisco, CA 94103'
    };

    await axios.put(
      `${BASE_URL}/profile/update-location`,
      updatedLocationData,
      {
        headers: { Authorization: `Bearer ${businessToken}` }
      }
    );
    console.log('✅ Business location updated successfully');
    console.log(`   New Location: ${updatedLocationData.formattedAddress}\n`);

    console.log('🎉 All location functionality tests passed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ Business user login');
    console.log('   ✅ Pet owner login');
    console.log('   ✅ Set business location');
    console.log('   ✅ Get business location');
    console.log('   ✅ Find nearby businesses');
    console.log('   ✅ Location-based business search');
    console.log('   ✅ Update business location');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testLocationFunctionality();
