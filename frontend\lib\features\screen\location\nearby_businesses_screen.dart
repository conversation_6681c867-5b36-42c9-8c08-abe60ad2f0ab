import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../provider/location_provider/location_provider.dart';
import '../../../provider/auth_provider/loginprovider.dart';
import '../../../utlis/constants/colors.dart';
import '../../../utlis/constants/size.dart';
import '../../../common/widgets/appbar/appbar.dart';

class NearbyBusinessesScreen extends StatefulWidget {
  const NearbyBusinessesScreen({super.key});

  @override
  State<NearbyBusinessesScreen> createState() => _NearbyBusinessesScreenState();
}

class _NearbyBusinessesScreenState extends State<NearbyBusinessesScreen> {
  List<Map<String, dynamic>> _businesses = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadNearbyBusinesses();
  }

  void _loadNearbyBusinesses() async {
    final locationProvider =
        Provider.of<LocationProvider>(context, listen: false);
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);

    if (loginProvider.token == null) {
      _showErrorDialog('Authentication Error', 'Please login to continue.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final businesses =
          await locationProvider.getNearbyBusinesses(loginProvider.token!);
      setState(() {
        _businesses = businesses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog(
          'Error', 'Failed to load nearby businesses: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Nearby Businesses',
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
              ),
            )
          : _businesses.isEmpty
              ? _buildEmptyState()
              : _buildBusinessesList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppSizes.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: AppSizes.md),
            Text(
              'No Nearby Businesses Found',
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: AppSizes.sm),
            Text(
              'There are no businesses in your area at the moment. Try expanding your search radius or check back later.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            SizedBox(height: AppSizes.lg),
            ElevatedButton.icon(
              onPressed: _loadNearbyBusinesses,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessesList() {
    return RefreshIndicator(
      onRefresh: () async {
        _loadNearbyBusinesses();
      },
      child: ListView.builder(
        padding: EdgeInsets.all(AppSizes.md),
        itemCount: _businesses.length,
        itemBuilder: (context, index) {
          final business = _businesses[index];
          return _buildBusinessCard(business);
        },
      ),
    );
  }

  Widget _buildBusinessCard(Map<String, dynamic> business) {
    return Card(
      margin: EdgeInsets.only(bottom: AppSizes.md),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSizes.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Business name and distance
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    business['name'] ?? 'Unknown Business',
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                  ),
                ),
                if (business['distance'] != null)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppSizes.sm,
                      vertical: AppSizes.xs,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius:
                          BorderRadius.circular(AppSizes.borderRadiusSm),
                    ),
                    child: Text(
                      '${business['distance'].toStringAsFixed(1)} km',
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: AppSizes.sm),

            // Address
            if (business['formattedAddress'] != null)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: AppSizes.iconSm,
                    color: Colors.grey[600],
                  ),
                  SizedBox(width: AppSizes.xs),
                  Expanded(
                    child: Text(
                      business['formattedAddress'],
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ),
                ],
              ),

            // Business hours (if available)
            if (business['shopOpenTime'] != null &&
                business['shopCloseTime'] != null)
              Padding(
                padding: EdgeInsets.only(top: AppSizes.xs),
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: AppSizes.iconSm,
                      color: Colors.grey[600],
                    ),
                    SizedBox(width: AppSizes.xs),
                    Text(
                      '${business['shopOpenTime']} - ${business['shopCloseTime']}',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ],
                ),
              ),

            SizedBox(height: AppSizes.md),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewBusinessDetails(business),
                    icon: const Icon(Icons.info_outline),
                    label: const Text('View Details'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppSizes.borderRadiusSm),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: AppSizes.sm),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _getDirections(business),
                    icon: const Icon(Icons.directions),
                    label: const Text('Directions'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppSizes.borderRadiusSm),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _viewBusinessDetails(Map<String, dynamic> business) {
    // TODO: Navigate to business details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Business details for ${business['name']} - Coming soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _getDirections(Map<String, dynamic> business) {
    // TODO: Open Google Maps with directions
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text('Getting directions to ${business['name']} - Coming soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
