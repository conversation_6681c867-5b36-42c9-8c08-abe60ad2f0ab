{"logs": [{"outputFile": "com.example.petcare.app-mergeDebugResources-45:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\895788cfce406ee7adbc1ad1d8f4cebb\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2797,2895,2997,3098,3196,3301,3413,6627", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "2890,2992,3093,3191,3296,3408,3527,6723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a800a360ad9d097b56329a7af7139d0\\transformed\\browser-1.8.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5817,6012,6116,6221", "endColumns": "104,103,104,107", "endOffsets": "5917,6111,6216,6324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19338aeb57a6075f97d371116308faf5\\transformed\\appcompat-1.1.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,899,990,1082,1177,1273,1371,1464,1558,1650,1741,1831,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,81,90,91,94,95,97,92,93,91,90,89,78,106,102,96,106,101,112,158,98,79", "endOffsets": "214,314,423,509,615,729,812,894,985,1077,1172,1268,1366,1459,1553,1645,1736,1826,1905,2012,2115,2212,2319,2421,2534,2693,2792,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,899,990,1082,1177,1273,1371,1464,1558,1650,1741,1831,1910,2017,2120,2217,2324,2426,2539,2698,6547", "endColumns": "113,99,108,85,105,113,82,81,90,91,94,95,97,92,93,91,90,89,78,106,102,96,106,101,112,158,98,79", "endOffsets": "214,314,423,509,615,729,812,894,985,1077,1172,1268,1366,1459,1553,1645,1736,1826,1905,2012,2115,2212,2319,2421,2534,2693,2792,6622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fb89e9541d65ec02c19ff3a6e8c0440\\transformed\\jetified-play-services-base-18.5.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3532,3642,3799,3931,4038,4175,4301,4430,4690,4834,4941,5109,5238,5379,5547,5608,5670", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "3637,3794,3926,4033,4170,4296,4425,4535,4829,4936,5104,5233,5374,5542,5603,5665,5742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1a74bb36ab68b4d1ac52e597a10bbe4d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4540", "endColumns": "149", "endOffsets": "4685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6bb2d0e97b623151957e955274b89d8a\\transformed\\preference-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,265,345,483,652,737", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "170,260,340,478,647,732,814"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5747,5922,6329,6409,6728,6897,6982", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "5812,6007,6404,6542,6892,6977,7059"}}]}]}