-- Merging decision tree log ---
application
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:5:5-39:19
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecbd1162972ca0cddd0174e383a0ad66\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecbd1162972ca0cddd0174e383a0ad66\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:31:5-34:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a36d88f7c8c3777f368d072782335c81\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a36d88f7c8c3777f368d072782335c81\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e93299a1fc1083020d09195db26417f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e93299a1fc1083020d09195db26417f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
	android:extractNativeLibs
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:1:1-51:12
MERGED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:1:1-51:12
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:google_maps_flutter_android] D:\flutter project\petdash\petdash\frontend\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] D:\flutter project\petdash\petdash\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] D:\flutter project\petdash\petdash\frontend\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:path_provider_android] D:\flutter project\petdash\petdash\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] D:\flutter project\petdash\petdash\frontend\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] D:\flutter project\petdash\petdash\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecbd1162972ca0cddd0174e383a0ad66\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bb2d0e97b623151957e955274b89d8a\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a800a360ad9d097b56329a7af7139d0\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af911bb8047dda09a5a7ae5b677d22ce\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4c0d961fc8f460e65d6c6d69bde0c95\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\653fc8f53c3914f62a0f3bbff79890c1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43e2e3f2fffb6df1ce60c1c7480f1cad\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:16:1-35:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a36d88f7c8c3777f368d072782335c81\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19338aeb57a6075f97d371116308faf5\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec5b209e2acfa0f9db467e3b053bed36\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fda9668f2fd9b3cd682e1fa054a93a4\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab0745a02aaa1ccd18c5479cb2d24b7d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b36f81d5694734e678bc98bc2922293\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\341ff4e8a072f5244d8eb5374273a29e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec888b5a3b521d35b918bfa813135035\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\863d903d4196e71f038154cdcf26a44e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53ddf7f36a3ebf2cbb00af1d3b42a284\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56aa3cd1a466adf05663b2732a4f7e8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\925e3ea7856a8fc98b3dd29a06d931f9\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9deae870e9ae7b9eda2566717208a9c7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bc594fc1c6cb8ef3ce5ef9c2c4d216b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8a2aedcd3468911daec058ed5ad1526\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09b85dd17d4915a9143d2ff48eb51df1\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5e10a490c81eb47dd4b0bd61ded4fb2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e0e47350d5dc007d1c6cc0b44591d20\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0bc073b85b7af5134751221919d7c1e0\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c08d71cb0270a0fe38ee94aafcf1084\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\062134c277dabaef3bb49730b9da0cf6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e66da08b8350b06b9e65dcae5172d0aa\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\572b3cd529ad8d1f50c9ea073d1b1282\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f730b396a5c8f0125d79bb117c74bc4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3ae16ee27885ecc08cd9a51819382d\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6b535042d7600ff2bbbfb7cc36f8317\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f48923f13f0c5e3f5f62ccecabf38eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa76d0f78bb809f455e8bec83704c9ab\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cdc514e15ae07e985d7d41474d8fe17\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\224149d087c6063137c304dd4ea01401\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\132654e60c110beebd6577371c8d1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f3ff5fbde88378ac9329e3a34b2575\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e93299a1fc1083020d09195db26417f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e50a35c7814c729c53cafc9088873669\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\61cf138f6574fd5bba9c4ff17c6b11c8\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae183f3b4adbe7555ac80fe89f9ebc91\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b12fe475519902724a9e13f377b78c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c12dbd7daa1121b50044912eb61a61b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8019a4ea5f3c1c1b82ebccb1c4ee11e2\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\782cabf1bc25f774abfb00a85b14454e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffafaf58ff7016d980e755b57fe58e4f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ab99850fd282aa118ae09c808d6df40\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d61f355b64f520cad0b84738b94eb20e\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b55a7e361e8e5445b27692ec9c2e406\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b99cb07b1848a37b8e3e132dbbe819b\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:5-66
MERGED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:5-66
MERGED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:5-66
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:21:5-66
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:21:5-66
	android:name
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:3:5-79
	android:name
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:3:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:4:5-81
	android:name
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:4:22-78
queries
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:45:5-50:15
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:27:5-30:15
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:27:5-30:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:46:9-49:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:47:13-72
	android:name
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:47:21-70
data
ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:48:13-50
	android:mimeType
		ADDED from D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:48:19-48
uses-sdk
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
MERGED from [:google_maps_flutter_android] D:\flutter project\petdash\petdash\frontend\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] D:\flutter project\petdash\petdash\frontend\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\flutter project\petdash\petdash\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\flutter project\petdash\petdash\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] D:\flutter project\petdash\petdash\frontend\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] D:\flutter project\petdash\petdash\frontend\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\flutter project\petdash\petdash\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\flutter project\petdash\petdash\frontend\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\flutter project\petdash\petdash\frontend\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\flutter project\petdash\petdash\frontend\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\flutter project\petdash\petdash\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\flutter project\petdash\petdash\frontend\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecbd1162972ca0cddd0174e383a0ad66\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ecbd1162972ca0cddd0174e383a0ad66\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bb2d0e97b623151957e955274b89d8a\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bb2d0e97b623151957e955274b89d8a\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a800a360ad9d097b56329a7af7139d0\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a800a360ad9d097b56329a7af7139d0\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af911bb8047dda09a5a7ae5b677d22ce\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\af911bb8047dda09a5a7ae5b677d22ce\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4c0d961fc8f460e65d6c6d69bde0c95\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e4c0d961fc8f460e65d6c6d69bde0c95\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\653fc8f53c3914f62a0f3bbff79890c1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\653fc8f53c3914f62a0f3bbff79890c1\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43e2e3f2fffb6df1ce60c1c7480f1cad\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\43e2e3f2fffb6df1ce60c1c7480f1cad\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:26:5-43
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a36d88f7c8c3777f368d072782335c81\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a36d88f7c8c3777f368d072782335c81\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19338aeb57a6075f97d371116308faf5\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19338aeb57a6075f97d371116308faf5\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec5b209e2acfa0f9db467e3b053bed36\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec5b209e2acfa0f9db467e3b053bed36\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fda9668f2fd9b3cd682e1fa054a93a4\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fda9668f2fd9b3cd682e1fa054a93a4\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab0745a02aaa1ccd18c5479cb2d24b7d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\ab0745a02aaa1ccd18c5479cb2d24b7d\transformed\jetified-activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b36f81d5694734e678bc98bc2922293\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6b36f81d5694734e678bc98bc2922293\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\341ff4e8a072f5244d8eb5374273a29e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\341ff4e8a072f5244d8eb5374273a29e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec888b5a3b521d35b918bfa813135035\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ec888b5a3b521d35b918bfa813135035\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\863d903d4196e71f038154cdcf26a44e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\863d903d4196e71f038154cdcf26a44e\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53ddf7f36a3ebf2cbb00af1d3b42a284\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\53ddf7f36a3ebf2cbb00af1d3b42a284\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56aa3cd1a466adf05663b2732a4f7e8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e56aa3cd1a466adf05663b2732a4f7e8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\925e3ea7856a8fc98b3dd29a06d931f9\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\925e3ea7856a8fc98b3dd29a06d931f9\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9deae870e9ae7b9eda2566717208a9c7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9deae870e9ae7b9eda2566717208a9c7\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bc594fc1c6cb8ef3ce5ef9c2c4d216b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6bc594fc1c6cb8ef3ce5ef9c2c4d216b\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8a2aedcd3468911daec058ed5ad1526\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8a2aedcd3468911daec058ed5ad1526\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09b85dd17d4915a9143d2ff48eb51df1\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\09b85dd17d4915a9143d2ff48eb51df1\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5e10a490c81eb47dd4b0bd61ded4fb2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5e10a490c81eb47dd4b0bd61ded4fb2\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e0e47350d5dc007d1c6cc0b44591d20\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e0e47350d5dc007d1c6cc0b44591d20\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0bc073b85b7af5134751221919d7c1e0\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0bc073b85b7af5134751221919d7c1e0\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c08d71cb0270a0fe38ee94aafcf1084\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c08d71cb0270a0fe38ee94aafcf1084\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\062134c277dabaef3bb49730b9da0cf6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\062134c277dabaef3bb49730b9da0cf6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e66da08b8350b06b9e65dcae5172d0aa\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e66da08b8350b06b9e65dcae5172d0aa\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\572b3cd529ad8d1f50c9ea073d1b1282\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\572b3cd529ad8d1f50c9ea073d1b1282\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f730b396a5c8f0125d79bb117c74bc4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f730b396a5c8f0125d79bb117c74bc4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3ae16ee27885ecc08cd9a51819382d\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1b3ae16ee27885ecc08cd9a51819382d\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6b535042d7600ff2bbbfb7cc36f8317\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6b535042d7600ff2bbbfb7cc36f8317\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f48923f13f0c5e3f5f62ccecabf38eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3f48923f13f0c5e3f5f62ccecabf38eb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa76d0f78bb809f455e8bec83704c9ab\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa76d0f78bb809f455e8bec83704c9ab\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cdc514e15ae07e985d7d41474d8fe17\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9cdc514e15ae07e985d7d41474d8fe17\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\224149d087c6063137c304dd4ea01401\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\224149d087c6063137c304dd4ea01401\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\132654e60c110beebd6577371c8d1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\132654e60c110beebd6577371c8d1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f3ff5fbde88378ac9329e3a34b2575\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f3ff5fbde88378ac9329e3a34b2575\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e93299a1fc1083020d09195db26417f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7e93299a1fc1083020d09195db26417f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e50a35c7814c729c53cafc9088873669\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e50a35c7814c729c53cafc9088873669\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\61cf138f6574fd5bba9c4ff17c6b11c8\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\61cf138f6574fd5bba9c4ff17c6b11c8\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae183f3b4adbe7555ac80fe89f9ebc91\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae183f3b4adbe7555ac80fe89f9ebc91\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b12fe475519902724a9e13f377b78c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b12fe475519902724a9e13f377b78c9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c12dbd7daa1121b50044912eb61a61b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c12dbd7daa1121b50044912eb61a61b2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8019a4ea5f3c1c1b82ebccb1c4ee11e2\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8019a4ea5f3c1c1b82ebccb1c4ee11e2\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\782cabf1bc25f774abfb00a85b14454e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\782cabf1bc25f774abfb00a85b14454e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffafaf58ff7016d980e755b57fe58e4f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffafaf58ff7016d980e755b57fe58e4f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ab99850fd282aa118ae09c808d6df40\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2ab99850fd282aa118ae09c808d6df40\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d61f355b64f520cad0b84738b94eb20e\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d61f355b64f520cad0b84738b94eb20e\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b55a7e361e8e5445b27692ec9c2e406\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b55a7e361e8e5445b27692ec9c2e406\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b99cb07b1848a37b8e3e132dbbe819b\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\3b99cb07b1848a37b8e3e132dbbe819b\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\flutter project\petdash\petdash\frontend\android\app\src\debug\AndroidManifest.xml
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:20:5-78
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:20:22-76
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:22:5-24:33
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:23:8-40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:24:8-31
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:7-61
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:16-59
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:7-86
	android:required
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:59-83
	android:name
		ADDED from [com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:21-58
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1c8eb61ac0ed7dd8ec211f40efa69f8\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\032b4ea919cde9099094bfb2c7bf01c1\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
