import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../utlis/constants/colors.dart';
import '../../../utlis/constants/size.dart';
import 'google_map_screen.dart';

class LocationSelectionModal extends StatefulWidget {
  const LocationSelectionModal({super.key});

  @override
  State<LocationSelectionModal> createState() => _LocationSelectionModalState();
}

class _LocationSelectionModalState extends State<LocationSelectionModal> {
  String selectedTab = 'Home';

  final List<Map<String, String>> homeAddresses = [
    {
      'title': '25 Peterborough Street',
      'subtitle': 'MA 02215, USA',
    },
  ];

  final List<Map<String, String>> workAddresses = [
    {
      'title': '25 Peterborough Street',
      'subtitle': 'MA 02215, USA',
    },
  ];

  final List<Map<String, String>> otherAddresses = [
    {
      'title': '25 Peterborough Street',
      'subtitle': 'MA 02215, USA',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(AppSizes.md),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 40.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2.r),
                    ),
                  ),
                ),
                SizedBox(height: AppSizes.md),
                Text(
                  'Choose Your Location',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.black,
                      ),
                ),
                SizedBox(height: AppSizes.xs),
                Text(
                  'Select a delivery location to see product availability and delivery options',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textPrimaryColor,
                      ),
                ),
              ],
            ),
          ),

          // Tabs
          Container(
            padding: EdgeInsets.symmetric(horizontal: AppSizes.md),
            child: Row(
              children: [
                _buildTab('Home'),
                SizedBox(width: AppSizes.sm),
                _buildTab('Work'),
                SizedBox(width: AppSizes.sm),
                _buildTab('Other'),
              ],
            ),
          ),

          SizedBox(height: AppSizes.md),

          // Address List
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildAddressList(),

                  // Use my current location
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: AppSizes.md),
                    child: ListTile(
                      leading: Icon(
                        Icons.my_location,
                        color: AppColors.primary,
                        size: AppSizes.iconMd,
                      ),
                      title: Text(
                        'Use my current location',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16.sp,
                        color: AppColors.textPrimaryColor,
                      ),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const GoogleMapScreen(),
                          ),
                        );
                      },
                    ),
                  ),

                  // Add New Address
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: AppSizes.md),
                    child: ListTile(
                      leading: Icon(
                        Icons.add,
                        color: Colors.red,
                        size: AppSizes.iconMd,
                      ),
                      title: Text(
                        'Add New Address',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        size: 16.sp,
                        color: AppColors.textPrimaryColor,
                      ),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const GoogleMapScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title) {
    final isSelected = selectedTab == title;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedTab = title;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppSizes.md,
          vertical: AppSizes.sm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[300]!,
          ),
        ),
        child: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isSelected ? Colors.white : AppColors.textPrimaryColor,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
        ),
      ),
    );
  }

  Widget _buildAddressList() {
    List<Map<String, String>> addresses;
    switch (selectedTab) {
      case 'Work':
        addresses = workAddresses;
        break;
      case 'Other':
        addresses = otherAddresses;
        break;
      default:
        addresses = homeAddresses;
    }

    return Column(
      children: addresses.map((address) {
        return Container(
          margin: EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: 4.h),
          child: ListTile(
            title: Text(
              address['title']!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.black,
                  ),
            ),
            subtitle: Text(
              address['subtitle']!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimaryColor,
                  ),
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: AppColors.textPrimaryColor,
            ),
            onTap: () {
              Navigator.of(context).pop();
              // Handle address selection - you can add logic here to save the selected address
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Selected: ${address['title']}'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
          ),
        );
      }).toList(),
    );
  }
}
