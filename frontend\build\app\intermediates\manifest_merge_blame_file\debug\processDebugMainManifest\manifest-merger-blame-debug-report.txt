1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.petcare"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:5-66
15-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:2:22-64
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:3:5-79
16-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:3:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:4:5-81
17-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:4:22-78
18    <!--
19 Required to query activities that can process text, see:
20         https://developer.android.com/training/package-visibility and
21         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
22
23         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
24    -->
25    <queries>
25-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:45:5-50:15
26        <intent>
26-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:46:9-49:18
27            <action android:name="android.intent.action.PROCESS_TEXT" />
27-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:47:13-72
27-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:47:21-70
28
29            <data android:mimeType="text/plain" />
29-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:48:13-50
29-->D:\flutter project\petdash\petdash\frontend\android\app\src\main\AndroidManifest.xml:48:19-48
30        </intent>
31        <!-- Needs to be explicitly declared on Android R+ -->
32        <package android:name="com.google.android.apps.maps" />
32-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:7-61
32-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:29:16-59
33    </queries> <!-- Include required permissions for Google Maps API to run. -->
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:20:5-78
34-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:20:22-76
35
36    <uses-feature
36-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:22:5-24:33
37        android:glEsVersion="0x00020000"
37-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:23:8-40
38        android:required="true" />
38-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:24:8-31
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.example.petcare.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45
46    <application
47        android:name="android.app.Application"
48        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\895788cfce406ee7adbc1ad1d8f4cebb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
49        android:debuggable="true"
50        android:enableOnBackInvokedCallback="true"
51        android:extractNativeLibs="true"
52        android:icon="@mipmap/ic_launcher"
53        android:label="petcare" >
54        <activity
55            android:name="com.example.petcare.MainActivity"
56            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57            android:exported="true"
58            android:hardwareAccelerated="true"
59            android:launchMode="singleTop"
60            android:taskAffinity=""
61            android:theme="@style/LaunchTheme"
62            android:windowSoftInputMode="adjustResize" >
63
64            <!--
65                 Specifies an Android theme to apply to this Activity as soon as
66                 the Android process has started. This theme is visible to the user
67                 while the Flutter UI initializes. After that, this theme continues
68                 to determine the Window background behind the Flutter UI.
69            -->
70            <meta-data
71                android:name="io.flutter.embedding.android.NormalTheme"
72                android:resource="@style/NormalTheme" />
73
74            <intent-filter>
75                <action android:name="android.intent.action.MAIN" />
76
77                <category android:name="android.intent.category.LAUNCHER" />
78            </intent-filter>
79        </activity>
80        <!--
81             Don't delete the meta-data below.
82             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
83        -->
84        <meta-data
85            android:name="flutterEmbedding"
86            android:value="2" />
87        <meta-data
88            android:name="com.google.android.geo.API_KEY"
89            android:value="AIzaSyDXLquInbZLHKdE7s2UolTfUbfbl3oj-w0" />
90
91        <provider
91-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
92            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
92-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
93            android:authorities="com.example.petcare.flutter.image_provider"
93-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
94            android:exported="false"
94-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
95            android:grantUriPermissions="true" >
95-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
96            <meta-data
96-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
97                android:name="android.support.FILE_PROVIDER_PATHS"
97-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
98                android:resource="@xml/flutter_image_picker_file_paths" />
98-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
99        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
100        <service
100-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
101            android:name="com.google.android.gms.metadata.ModuleDependencies"
101-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
102            android:enabled="false"
102-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
103            android:exported="false" >
103-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
104            <intent-filter>
104-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
105                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
105-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
105-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
106            </intent-filter>
107
108            <meta-data
108-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
109                android:name="photopicker_activity:0:required"
109-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
110                android:value="" />
110-->[:image_picker_android] D:\flutter project\petdash\petdash\frontend\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
111        </service>
112        <service
112-->[:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
113            android:name="com.baseflow.geolocator.GeolocatorLocationService"
113-->[:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
114            android:enabled="true"
114-->[:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
115            android:exported="false"
115-->[:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
116            android:foregroundServiceType="location" />
116-->[:geolocator_android] D:\flutter project\petdash\petdash\frontend\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
117
118        <activity
118-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
119            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
119-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
120            android:exported="false"
120-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
121            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
121-->[:url_launcher_android] D:\flutter project\petdash\petdash\frontend\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
122
123        <uses-library
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
124            android:name="androidx.window.extensions"
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
125            android:required="false" />
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
126        <uses-library
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
127            android:name="androidx.window.sidecar"
127-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
128            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
128-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0b5531fb6071dd0c0637bf9e3ff30b3\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
129        <uses-library
129-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:7-86
130            android:name="org.apache.http.legacy"
130-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:21-58
131            android:required="false" />
131-->[com.google.android.gms:play-services-maps:19.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc60dd613cba8cc6fc95f4da8c63714\transformed\jetified-play-services-maps-19.2.0\AndroidManifest.xml:33:59-83
132
133        <activity
133-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
134            android:name="com.google.android.gms.common.api.GoogleApiActivity"
134-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
135            android:exported="false"
135-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
136-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\3fb89e9541d65ec02c19ff3a6e8c0440\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
137
138        <meta-data
138-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
139            android:name="com.google.android.gms.version"
139-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
140            android:value="@integer/google_play_services_version" />
140-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a74bb36ab68b4d1ac52e597a10bbe4d\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
141
142        <provider
142-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
143            android:name="androidx.startup.InitializationProvider"
143-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
144            android:authorities="com.example.petcare.androidx-startup"
144-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
145            android:exported="false" >
145-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
146            <meta-data
146-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
147                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
147-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
148                android:value="androidx.startup" />
148-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dd5fd3a0ceb20921a62658cb946f6c44\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
151                android:value="androidx.startup" />
151-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
152        </provider>
153
154        <receiver
154-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
155            android:name="androidx.profileinstaller.ProfileInstallReceiver"
155-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
156            android:directBootAware="false"
156-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
157            android:enabled="true"
157-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
158            android:exported="true"
158-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
159            android:permission="android.permission.DUMP" >
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
161                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
164                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
165            </intent-filter>
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
167                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
167-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
168            </intent-filter>
169            <intent-filter>
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
170                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
170-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ff19eef5016a9a5eff52d2000cbb8b10\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
171            </intent-filter>
172        </receiver>
173    </application>
174
175</manifest>
