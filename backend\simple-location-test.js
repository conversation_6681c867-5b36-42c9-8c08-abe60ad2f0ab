const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testLocationAPI() {
  console.log('🧪 Testing Location API...\n');

  try {
    // Test 1: Login as business user
    console.log('1. Testing business user login...');
    const loginOptions = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const loginResponse = await makeRequest(loginOptions, loginData);
    
    if (loginResponse.status === 200 && loginResponse.data.token) {
      console.log('✅ Business user login successful');
      const token = loginResponse.data.token;
      console.log(`   Token received: ${token.substring(0, 20)}...\n`);

      // Test 2: Set business location
      console.log('2. Testing set business location...');
      const locationOptions = {
        hostname: 'localhost',
        port: 5000,
        path: '/api/profile/update-location',
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      };

      const locationData = {
        latitude: 37.7749,
        longitude: -122.4194,
        formattedAddress: '123 Test Business St, San Francisco, CA'
      };

      const locationResponse = await makeRequest(locationOptions, locationData);
      
      if (locationResponse.status === 200) {
        console.log('✅ Business location set successfully');
        console.log(`   Location: ${locationData.formattedAddress}`);
        console.log(`   Coordinates: ${locationData.latitude}, ${locationData.longitude}\n`);

        // Test 3: Get business location
        console.log('3. Testing get business location...');
        const getLocationOptions = {
          hostname: 'localhost',
          port: 5000,
          path: '/api/location/business-location',
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        };

        const getLocationResponse = await makeRequest(getLocationOptions);
        
        if (getLocationResponse.status === 200) {
          console.log('✅ Business location retrieved successfully');
          console.log(`   Has Location: ${getLocationResponse.data.location.hasLocation}`);
          console.log(`   Address: ${getLocationResponse.data.location.formattedAddress}\n`);
        } else {
          console.log('❌ Failed to get business location');
          console.log(`   Status: ${getLocationResponse.status}`);
          console.log(`   Response: ${JSON.stringify(getLocationResponse.data)}\n`);
        }

        // Test 4: Test nearby businesses search
        console.log('4. Testing nearby businesses search...');
        const nearbyOptions = {
          hostname: 'localhost',
          port: 5000,
          path: '/api/location/nearby-businesses',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        };

        const nearbyData = {
          latitude: 37.7849,
          longitude: -122.4094,
          radius: 10
        };

        const nearbyResponse = await makeRequest(nearbyOptions, nearbyData);
        
        if (nearbyResponse.status === 200) {
          console.log('✅ Nearby businesses search successful');
          console.log(`   Found ${nearbyResponse.data.businesses.length} businesses`);
          if (nearbyResponse.data.businesses.length > 0) {
            const business = nearbyResponse.data.businesses[0];
            console.log(`   First business: ${business.name}`);
            console.log(`   Distance: ${business.distance} km\n`);
          }
        } else {
          console.log('❌ Failed to search nearby businesses');
          console.log(`   Status: ${nearbyResponse.status}`);
          console.log(`   Response: ${JSON.stringify(nearbyResponse.data)}\n`);
        }

      } else {
        console.log('❌ Failed to set business location');
        console.log(`   Status: ${locationResponse.status}`);
        console.log(`   Response: ${JSON.stringify(locationResponse.data)}\n`);
      }

    } else {
      console.log('❌ Business user login failed');
      console.log(`   Status: ${loginResponse.status}`);
      console.log(`   Response: ${JSON.stringify(loginResponse.data)}\n`);
    }

    console.log('🎉 Location API test completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testLocationAPI();
