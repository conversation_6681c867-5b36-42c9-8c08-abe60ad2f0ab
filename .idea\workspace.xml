<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1353c25d-7094-4b07-9800-038a75bcafb3" name="Changes" comment="businessProfiles1">
      <change afterPath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/Screen/MyOrder/myorderScreen.dart" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/Screen/MyPet/AddAnotherPet.dart" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/Screen/Save Address/saveAddressScreen.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/android/app/src/main/AndroidManifest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/android/app/src/main/AndroidManifest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/auth/login/loginscreen.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/auth/login/loginscreen.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/AddNewArticles.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/AddNewArticles.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/EditArticlesdetails.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/EditArticlesdetails.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/MyArticles.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyArticles/MyArticles.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/AddNewClients.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/AddNewClients.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/EditClients.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/EditClients.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/myClients.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyClients/myClients.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/AddNewProducts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/AddNewProducts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/editNewProducts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/editNewProducts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/myproducts.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyProducts/myproducts.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/addnewServices.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/addnewServices.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/editServicesdetails.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/editServicesdetails.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/myServices.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/MyServices/myServices.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/Reports/StatisticsScreen.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/Reports/StatisticsScreen.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/Subscription/Subscription.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/Subscription/Subscription.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/order/ordercancle.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/order/ordercancle.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/order/orderdetails.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/order/orderdetails.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/Screen/MyPet/mypets.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/Screen/MyPet/mypets.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/profileScreen.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/personal/profile/profileScreen.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>/home_appbar.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>/home_appbar.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>/home_header_banner.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/features/screen/shop/home/<USER>/home_header_banner.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/main.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/main.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/lib/utlis/constants/size.dart" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/lib/utlis/constants/size.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/macos/Flutter/GeneratedPluginRegistrant.swift" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/macos/Flutter/GeneratedPluginRegistrant.swift" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/windows/flutter/generated_plugin_registrant.cc" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/windows/flutter/generated_plugin_registrant.cc" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/windows/flutter/generated_plugins.cmake" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/windows/flutter/generated_plugins.cmake" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="CMakeLists.txt" />
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;history&quot;: [
    {
      &quot;state&quot;: &quot;OPEN&quot;,
      &quot;reviewState&quot;: &quot;CHANGES_REQUESTED&quot;
    },
    {
      &quot;state&quot;: &quot;OPEN&quot;,
      &quot;assignee&quot;: &quot;Divy2003&quot;,
      &quot;reviewState&quot;: &quot;CHANGES_REQUESTED&quot;
    }
  ],
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Divy2003&quot;,
    &quot;reviewState&quot;: &quot;CHANGES_REQUESTED&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Divy2003/petdash.git&quot;,
    &quot;accountId&quot;: &quot;b4ff287d-3937-4e75-86a3-ac928625cd36&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30DL1t6GolmxHkw4MGxLZ92liXR" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Flutter.petdash.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;io.flutter.reload.alreadyRun&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/flutter project/petdash/petdash/frontend/assets/cards&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;dart.settings&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\flutter project\petdash\petdash\frontend\assets\cards" />
      <recent name="D:\flutter project\petdash\petdash\frontend\assets\products" />
      <recent name="D:\flutter project\petdash\petdash\frontend\assets\homeimage\store" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\flutter project\petdash\petdash\frontend\lib\features\screen\shop\home\Selectelocations" />
      <recent name="D:\flutter project\petdash\petdash\frontend\lib\features\screen\business\Screen\MyPaymentsMethod" />
      <recent name="D:\flutter project\petdash\petdash\frontend\lib\features\screen\business\Screen\MyArticles" />
      <recent name="D:\flutter project\petdash\petdash\frontend\assets" />
      <recent name="D:\flutter project\petdash\petdash\frontend\assets\person" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="petdash" type="FlutterRunConfigurationType" factoryName="Flutter">
      <option name="filePath" value="$PROJECT_DIR$/frontend/lib/main.dart" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1353c25d-7094-4b07-9800-038a75bcafb3" name="Changes" comment="" />
      <created>1753156987185</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753156987185</updated>
    </task>
    <task id="LOCAL-00001" summary="second">
      <option name="closed" value="true" />
      <created>1753167506926</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753167506926</updated>
    </task>
    <task id="LOCAL-00002" summary="business">
      <option name="closed" value="true" />
      <created>1753264348225</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753264348225</updated>
    </task>
    <task id="LOCAL-00003" summary="businessProfiles">
      <option name="closed" value="true" />
      <created>1753273797889</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753273797890</updated>
    </task>
    <task id="LOCAL-00004" summary="businessProfiles1">
      <option name="closed" value="true" />
      <created>1753671392622</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753671392622</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="second" />
    <MESSAGE value="business" />
    <MESSAGE value="businessProfiles" />
    <MESSAGE value="businessProfiles1" />
    <option name="LAST_COMMIT_MESSAGE" value="businessProfiles1" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="Dart">
          <url>file://$PROJECT_DIR$/frontend/lib/features/screen/business/Screen/Appoinments/AppointmentsDetails.dart</url>
          <line>281</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>